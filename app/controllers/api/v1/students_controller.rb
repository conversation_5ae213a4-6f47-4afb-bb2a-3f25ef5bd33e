# frozen_string_literal: true

class Api::V1::StudentsController < ApplicationController
  include Pagination

  def index
    authorize! :read, User

    students = fetch_students

    students = students.where(search_query, search: "%#{params[:search]}%") if params[:search].present? && params[:search].length >= 3

    students = students.order("#{sort_key} #{sort_order}")
    @paginated_data = paginated(students, pagination_params)
  end

  def create_observer_link
    authorize! :create, StudentObserverLink

    student = User.find_by(canvas_id: params[:id])
    return render json: { error: 'Student not found' }, status: :not_found unless student

    # First, cleanup any expired links
    StudentObserverLink.needs_cleanup.for_observer(current_canvas_user_id).find_each(&:mark_expired!)

    # Check if observer already has an active link
    existing_link = StudentObserverLink.active.for_observer(current_canvas_user_id).first
    if existing_link
      return render json: {
        error: 'You already have an active observer link. Please end the current link before creating a new one.'
      }, status: :unprocessable_entity
    end

    # Create the observer link
    observer_link = StudentObserverLink.new(
      observer_user_id: current_canvas_user_id,
      observed_student_id: student.canvas_id,
      expires_at: Time.now + 1.hours
    )

    if observer_link.save
      render json: {
        data: format_observer_link(observer_link),
        message: 'Observer link created successfully'
      }, status: :created
    else
      render json: {
        error: observer_link.errors.full_messages.join(', ')
      }, status: :unprocessable_entity
    end
  end

  private

  def fetch_students
    base_query = if current_ability.launch_context.is_a?(Account)
                   User.active_students_for_account(current_ability.launch_context)
                 elsif current_ability.launch_context.is_a?(Course)
                   User.active_students_in_course_ids([current_ability.launch_context])
                 else
                   User.none
                 end

    # Join with pseudonyms for SIS ID search
    base_query.left_joins(:pseudonyms)
  end

  def format_observer_link(observer_link)
    {
      id: observer_link.id,
      observer_user_id: observer_link.observer_user_id,
      observed_student: {
        canvas_id: observer_link.observed_student.canvas_id,
        sortable_name: observer_link.observed_student.sortable_name,
        sis_id: observer_link.observed_student.pseudonyms.first&.sis_id
      },
      created_at: observer_link.created_at,
      expires_at: observer_link.expires_at,
      renewed_at: observer_link.renewed_at,
      time_remaining_minutes: observer_link.time_remaining,
      can_be_renewed: observer_link.can_be_renewed?,
      status: observer_link.status
    }
  end

  def search_query
    <<~SQL
      users.first_name ILIKE :search OR
      users.last_name ILIKE :search OR
      users.sortable_name ILIKE :search OR
      pseudonyms.sis_id ILIKE :search
    SQL
  end

  def sort_key
    return params[:sort_key] if %w[users.sortable_name].include?(params[:sort_key])

    'users.sortable_name'
  end

  def sort_order
    params[:sort_order].to_s.upcase.in?(%w[ASC DESC]) ? params[:sort_order] : 'ASC'
  end
end
